import dayjs from 'dayjs'
export type TaskItem = {
  taskName: string
  id: string
}
/**
 * 任务状态枚举
 */
export enum TaskStatus {
  PENDING = 'pending',
  PROCESSING = 'inProgress',
  COMPLETED = 'completed',
  CANCELED = 'cancelled'
}

/**
 * 任务类型枚举
 */
export enum TaskType {
  MAINTENANCE = 'maintenance',
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  DESIGN = 'design',
  MEETING = 'meeting'
}

/**
 * 格式化日期时间
 * @param {string|Date} date 日期时间
 * @param {string} format 格式化模板
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTime(date: string | Date | null | undefined, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!date) return '';
  return dayjs(date).format(format);
}

/**
 * 获取任务状态标签类型
 * @param {string} status 任务状态
 * @returns {string} 对应的标签类型
 */
export function getTaskStatusType(status: string): string {
  switch(status) {
    case TaskStatus.PENDING:
      return 'info';
    case TaskStatus.PROCESSING:
      return 'primary';
    case TaskStatus.COMPLETED:
      return 'success';
    case TaskStatus.CANCELED:
      return 'danger';
    default:
      return 'info';
  }
}

/**
 * 获取任务状态选项
 * @param {Function} t 国际化翻译函数
 * @returns {Array} 状态选项数组
 */
export function getTaskStatusOptions(t?: Function): Array<{label: string, value: string}> {
  return Object.values(TaskStatus).map(status => ({
    label: t ? t(`task.status.${status}`) : status,
    value: status
  }));
}

/**
 * 获取任务类型选项
 * @param {Function} t 国际化翻译函数
 * @returns {Array} 类型选项数组
 */
export function getTaskTypeOptions(t?: Function): Array<{label: string, value: string}> {
  return Object.values(TaskType).map(type => ({
    label: t ? t(`task.type.${type}`) : type,
    value: type
  }));
}

/**
 * 创建初始任务对象
 * @returns {Object} 初始任务对象
 */
export function createInitialTask(): any {
  return {
    taskName: '',
    assignee: '',
    status: TaskStatus.PENDING,
    type: '',
    plannedStartTime: '',
    plannedEndTime: ''
  };
}