/**
 * URL 辅助工具
 * 用于生成各种类型的 URL，包括内容页直接访问链接
 */

/**
 * 检查当前环境是否为生产环境
 * @returns {boolean} 是否为生产环境
 */
export const isProduction = (): boolean => {
  return import.meta.env.MODE === 'production';
};

/**
 * 生成内容页直接访问的URL
 * @param {string} path - 路由路径
 * @param {Record<string, string>} [params={}] - URL参数
 * @returns {string} 内容页直接访问的URL
 */
export const getContentPageUrl = (path: string, params: Record<string, string> = {}): string => {
  // 如果不是生产环境，返回原始路径
  if (!isProduction()) {
    return path;
  }

  // 移除开头的斜杠
  const relativePath = path.startsWith('/') ? path.substring(1) : path;
  const contentPath = `/content/${relativePath}`;
  
  // 添加URL参数
  if (Object.keys(params).length > 0) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      searchParams.append(key, value);
    });
    return `${contentPath}?${searchParams.toString()}`;
  }
  
  return contentPath;
};

/**
 * 为当前URL添加空布局参数
 * @param {string} url - 原始URL
 * @returns {string} 添加了空布局参数的URL
 */
export const addEmptyLayoutParam = (url: string): string => {
  const urlObj = new URL(url, window.location.origin);
  urlObj.searchParams.set('empty_layout', 'true');
  return urlObj.toString();
};
