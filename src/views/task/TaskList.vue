<script lang="ts" setup>
import { getTaskPage, batchDeleteTask } from '@/api/task'
import { computed, ref, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { omitBy, isUndefined } from 'lodash-es'
import { useI18n } from 'vue-i18n'
import {
  TaskItem,
  formatDateTime,
  getTaskStatusOptions,
  getTaskStatusType,
  getTaskTypeOptions
} from '@/utils/taskUtils'
import { useBreadcrumbsStore } from '@/stores/useBreadcrumbsStore'
// @ts-ignore
import { useTrinaTable } from 'trina-design-ui/src'

const { t: $t } = useI18n()
const breadcrumbsStore = useBreadcrumbsStore()
breadcrumbsStore.updateBreadcrumbs([
  {
    name: $t('task.title'),
    path: ''
  },
  {
    name: $t('task.list'),
    path: ''
  }
])

const router = useRouter()

//#region 筛选条件
// 任务类型和状态选项
const taskTypeOptions = getTaskTypeOptions($t)
const taskStatusOptions = getTaskStatusOptions($t)
const searchFields = [
  { prop: 'id', label: $t('task.columns.id') },
  {
    prop: 'type',
    label: $t('task.columns.type'),
    format: 'singleList',
    props: { options: taskTypeOptions }
  },
  {
    prop: 'status',
    label: $t('task.columns.status'),
    format: 'singleList',
    props: { options: taskStatusOptions }
  },
  { prop: 'assignee', label: $t('task.columns.assignee') },
  { prop: 'createTime', label: $t('task.columns.createTime') }
]
//#endregion

//#region 表格配置
// 表格列定义
const columns = [
  { prop: 'id', label: $t('task.columns.id'), sortable: false, width: 180 },
  { prop: 'taskName', label: $t('task.columns.taskName'), minWidth: 150 },
  { prop: 'assignee', label: $t('task.columns.assignee'), width: 120 , formatter: 'Person'},
  { prop: 'type', label: $t('task.columns.type'), width: 120 },
  { prop: 'status', label: $t('task.columns.status'), width: 100, slotName: 'status' },
  { prop: 'plannedStartTime', label: $t('task.columns.plannedStartTime'), width: 180 },
  { prop: 'plannedEndTime', label: $t('task.columns.plannedEndTime'), width: 180 },
  { prop: 'createTime', label: $t('task.columns.createTime'), width: 180 }
]

const fetchData = async (page: any, orders: any, query: any) => {
  const params = {
    size: page.pageSize,
    pageNo: page.currentPage,
    query: omitBy(query, v => isUndefined(v) || v === ''),
    ...orders
  }
  const res: any = await getTaskPage<TaskItem>(params)
  if (res.code == 0 && res.data.records) {
    const timeKeys = ['plannedStartTime', 'plannedEndTime', 'createTime']
    res.data.records = (res.data?.records || []).map((item: any) => {
      timeKeys.map(timeKey => {
        item[timeKey] = formatDateTime(item[timeKey])
      })
      return item
    })
  }

  return res?.data
}

const tableRef = ref()
const { tableProps, tableEvents } = useTrinaTable<TaskItem>(fetchData, {
  pageRef: tableRef
})
const { selectedRows } = toRefs(tableProps)
const selectedRowsIds = computed(() => tableProps.selectedRows?.map((i: any) => i.id))

const handleItemAction = (act: 'detail' | 'edit' | 'add' = 'detail', row?: TaskItem) => {
  const idStr = row ? `/${row.id}` : ''
  router.push(`/task/${act}${idStr}`)
}
const handleDelete = (rows: TaskItem[]) => {
  let desc =
    rows.length > 1
      ? $t('task.messages.confirmBatchDelete', { count: rows.length })
      : $t('task.messages.confirmDelete')
  ElMessageBox.confirm(desc, $t('common.confirm'), {
    confirmButtonText: $t('common.confirm'),
    cancelButtonText: $t('common.cancel'),
    type: 'warning',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        batchDeleteTask(rows.map(i => i.id))
          .then(res => {
            if (res?.data) ElMessage.success($t('task.messages.deleteSuccess'))
            tableRef.value?.reload(1)
          })
          .finally(() => {
            instance.confirmButtonLoading = false
            done()
          })
      } else {
        done()
      }
    }
  }).catch(() => {
    ElMessage.info($t('common.cancelled'))
  })
}
</script>

<template>
  <div class="table-page-container">
    <trina-table-page
      v-bind="{ ...tableProps, ...tableEvents }"
      ref="tableRef"
      :showIndex="false"
      :columns="columns"
      :searchFields="searchFields"
    >
      <template #actions>
        <div>
          <el-button type="primary" @click="handleItemAction('add')">{{
            $t('task.add')
          }}</el-button>
          <el-button
            type="danger"
            v-if="selectedRowsIds.length"
            @click="handleDelete(selectedRows)"
          >
            {{ $t('task.batchDelete') }}
          </el-button>
        </div>
      </template>

      <!-- 状态列自定义显示 -->
      <template #column-status="{ row }">
        <el-tag :type="getTaskStatusType(row.status)">{{
          row.status ? $t(`task.status.${row.status}`) : '-'
        }}</el-tag>
      </template>
      <!-- 类型自定义显示 -->
      <template #column-type="{ row }">
        <span>{{ row.type ? $t(`task.type.${row.type}`) : '-' }}</span>
      </template>

      <!-- 自定义操作列 -->
      <template #operations="{ row }">
        <el-space>
          <el-button type="primary" link @click="handleItemAction('detail', row)">
            <!-- <el-icon><view /></el-icon> -->
            <span>{{ $t('table.view') }}</span>
          </el-button>
          <el-button type="primary" link @click="handleItemAction('edit', row)">
            <!-- <el-icon><edit /></el-icon> -->
            <span>{{ $t('table.edit') }}</span>
          </el-button>
          <el-button
            :disabled="selectedRowsIds.includes(row.id)"
            type="danger"
            link
            @click="handleDelete([row])"
          >
            <!-- <el-icon><delete /></el-icon> -->
            <span>{{ $t('table.delete') }}</span>
          </el-button>
        </el-space>
      </template>
    </trina-table-page>
  </div>
</template>

<style scoped lang="scss">
.table-page-container {
  height: 100%;
  padding: 8px;
}
</style>
