import { defineConfig, loadEnv } from 'vite'
import { fileURLToPath, URL } from 'node:url'
import vue from '@vitejs/plugin-vue'
import qiankun from 'vite-plugin-qiankun'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  // 设置第三个参数为 '' 来加载所有环境变量，而不管是否有 `VITE_` 前缀
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
    plugins: [
      vue(),
      vueDevTools(),
      qiankun('trinaCli', { // 微应用名称
        useDevMode: true
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        'trina-design-ui': fileURLToPath(new URL('../trina-design-ui', import.meta.url))
      }
    },
    server: {
      // 从环境变量中获取端口，如果没有则使用默认端口 3000
      port: parseInt(env.VITE_APP_PORT || '3000'),
      // 自动打开浏览器
      open: true,
      // 代理配置
      proxy: {
        // 开发环境代理配置
        [env.VITE_APP_API_BASE_URL]: {
          target: 'http://116.62.103.168:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp('^' + env.VITE_APP_API_BASE_URL), '')
        }
      }
    },
    build: {
      // 输出目录
      outDir: 'dist',
      // 生成静态资源的存放路径
      assetsDir: 'assets',
      // 小于此阈值的导入或引用资源将内联为 base64 编码
      assetsInlineLimit: 4096,
      // 启用/禁用 CSS 代码拆分
      cssCodeSplit: true,
      // 构建后是否生成 source map 文件
      sourcemap: env.VITE_APP_SOURCEMAP === 'true',
      // 自定义底层的 Rollup 打包配置
      rollupOptions: {
        output: {
          // 静态资源分类打包
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
        }
      }
    }
  }
})
