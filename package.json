{"name": "trina-frontend-web", "version": "0.1.0", "private": true, "type": "module", "engines": {"node": ">=16.0.0", "npm": "please-use-yarn"}, "packageManager": "yarn@1.22.19", "scripts": {"dev": "vite --mode development --port 3000", "prod": "vite --mode production --port 3000", "build": "vue-tsc --noEmit && vite build --mode production", "build:test": "vue-tsc --noEmit && vite build --mode test", "build:dev": "vue-tsc --noEmit && vite build --mode development", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx --fix --ignore-path .gitignore", "format": "prettier --write src", "prepare": "husky install"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.7", "element-plus": "^2.8.5", "pinia": "^2.1.7", "trina-design-ui": "file:../trina-design-ui", "vite-plugin-qiankun": "^1.0.15", "vue": "^3.5.14", "vue-i18n": "^9.9.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.12.0", "@vitejs/plugin-vue": "^5.2.3", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.21.1", "husky": "^9.1.7", "sass": "^1.72.0", "sass-loader": "^14.1.1", "typescript": "^5.4.5", "unplugin-vue-components": "^28.5.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.0.6"}}